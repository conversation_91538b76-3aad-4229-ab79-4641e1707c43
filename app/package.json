{"name": "ai_memory", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@aws-sdk/client-bedrock-runtime": "^3.828.0", "@google/genai": "^1.5.0", "@google/generative-ai": "^0.24.1", "@mistralai/mistralai": "^1.7.2", "@modelcontextprotocol/sdk": "^1.12.1", "@qdrant/js-client-rest": "^1.14.1", "anthropic": "^0.0.0", "chromadb": "^3.0.3", "cloudflare": "^4.3.0", "cohere-ai": "^7.17.1", "dotenv": "^16.5.0", "express": "^5.1.0", "groq-sdk": "^0.25.0", "mem0ai": "^2.1.30", "neo4j-driver": "^5.28.1", "ollama": "^0.5.16", "openai": "^5.3.0", "redis": "^5.5.6", "sqlite3": "^5.1.7"}}