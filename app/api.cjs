const express = require('express')
const { spawn } = require('child_process')
const fs = require('fs').promises
const path = require('path')
require('dotenv').config()

console.log('🔧 Starting Mem0 REST API server...')
console.log('📊 Environment variables loaded')

const app = express()
const port = process.env.API_PORT || 3000

console.log(`🌐 Server will run on port: ${port}`)

// Middleware
app.use(express.json())
console.log('✅ Express middleware configured')

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ 
      error: 'Access token required',
      message: 'Please provide a Bearer token in the Authorization header'
    })
  }

  if (token !== process.env.API_BEARER_TOKEN) {
    return res.status(403).json({ 
      error: 'Invalid token',
      message: 'The provided token is not valid'
    })
  }

  next()
}

// Helper function to build user ID
const buildUserId = (userId, sessionId) => {
  if (!userId || !sessionId) {
    throw new Error('Both user_id and session_id are required')
  }
  return `${userId}_${sessionId}`
}

// Helper function to run mem0ai operations using subprocess
const runMem0Operation = async (operation, data) => {
  return new Promise((resolve, reject) => {
    const tempFile = path.join(process.cwd(), `temp_${operation}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}.json`)

    // Write operation data to temp file
    fs.writeFile(tempFile, JSON.stringify(data, null, 2))
      .then(() => {
        // Create the subprocess script
        const script = `
          const { Memory } = require('mem0ai/oss');
          const fs = require('fs');
          require('dotenv').config();

          const config = {
            llm: {
              provider: 'openai',
              config: {
                apiKey: process.env.OPENAI_API_KEY,
                model: process.env.OPENAI_LLM_MODEL,
                temperature: parseFloat(process.env.OPENAI_LLM_TEMPERATURE)
              }
            },
            embedder: {
              provider: 'openai',
              config: {
                model: process.env.OPENAI_EMBEDDING_MODEL,
                apiKey: process.env.OPENAI_API_KEY
              }
            },
            vectorStore: {
              provider: 'qdrant',
              config: {
                collectionName: process.env.QDRANT_COLLECTION_NAME,
                host: process.env.QDRANT_HOST,
                port: parseInt(process.env.QDRANT_PORT),
                embeddingModelDims: parseInt(process.env.QDRANT_EMBEDDING_DIMS)
              }
            },
            historyStore: {
              provider: 'sqlite',
              config: {
                historyDbPath: process.env.HISTORY_DB_PATH
              }
            },
            enableGraph: process.env.ENABLE_GRAPH === 'true',
            graphStore: {
              provider: 'neo4j',
              config: {
                url: process.env.NEO4J_URI,
                username: process.env.NEO4J_USERNAME,
                password: process.env.NEO4J_PASSWORD
              }
            }
          };

          const memory = new Memory(config);
          const operationData = JSON.parse(fs.readFileSync('${tempFile}', 'utf8'));

          if (operationData.operation === 'add') {
            memory.add(operationData.messages, { userId: operationData.userId, metadata: operationData.metadata || {}, version: 2 })
              .then(result => {
                console.log(JSON.stringify({ success: true, result }));
                process.exit(0);
              })
              .catch(error => {
                console.error(JSON.stringify({ success: false, error: error.message }));
                process.exit(1);
              });
          } else if (operationData.operation === 'search') {
            memory.search(operationData.query, { userId: operationData.userId, limit: operationData.limit })
              .then(result => {
                console.log(JSON.stringify({ success: true, result }));
                process.exit(0);
              })
              .catch(error => {
                console.error(JSON.stringify({ success: false, error: error.message }));
                process.exit(1);
              });
          } else {
            console.error(JSON.stringify({ success: false, error: 'Unknown operation' }));
            process.exit(1);
          }
        `

        const child = spawn('node', ['-e', script], { stdio: 'pipe' })

        let output = ''
        let errorOutput = ''

        child.stdout.on('data', (data) => {
          output += data.toString()
        })

        child.stderr.on('data', (data) => {
          errorOutput += data.toString()
        })

        child.on('close', (code) => {
          // Clean up temp file
          fs.unlink(tempFile).catch(() => {})

          console.log(`🔧 Subprocess completed with code: ${code}`)
          console.log(`🔧 Subprocess output: ${output}`)
          console.log(`🔧 Subprocess error: ${errorOutput}`)

          if (code === 0) {
            try {
              // Extract JSON from output (skip debug/info logs)
              const lines = output.trim().split('\n')
              const jsonLine = lines.find(line => line.startsWith('{') && line.includes('"success"'))

              if (jsonLine) {
                const result = JSON.parse(jsonLine)
                console.log(`🔧 Parsed result:`, JSON.stringify(result, null, 2))
                resolve(result)
              } else {
                console.log(`🔧 No JSON found in output, returning raw output`)
                resolve({ success: true, output })
              }
            } catch (e) {
              console.log(`🔧 Failed to parse JSON:`, e.message)
              resolve({ success: true, output })
            }
          } else {
            reject(new Error(errorOutput || 'Operation failed'))
          }
        })

        child.on('error', (error) => {
          // Clean up temp file
          fs.unlink(tempFile).catch(() => {})
          reject(error)
        })
      })
      .catch(reject)
  })
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'mem0-api'
  })
})

// POST /add - Add memories asynchronously
app.post('/add', authenticateToken, async (req, res) => {
  try {
    const { user_id, session_id, messages, metadata = {} } = req.body

    // Validate required parameters
    if (!user_id || !session_id) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both user_id and session_id are required'
      })
    }

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: 'Invalid messages parameter',
        message: 'messages must be a non-empty array'
      })
    }

    // Validate message format
    for (const message of messages) {
      if (!message.role || !message.content) {
        return res.status(400).json({
          error: 'Invalid message format',
          message: 'Each message must have "role" and "content" properties'
        })
      }
    }

    // Build full user ID
    const fullUserId = buildUserId(user_id, session_id)

    // Respond immediately to client
    res.status(202).json({
      status: 'accepted',
      message: 'Memories are being processed in the background',
      user_id: fullUserId,
      message_count: messages.length,
      timestamp: new Date().toISOString()
    })

    // Process memories asynchronously in background
    setImmediate(async () => {
      try {
        console.log(`🔄 Processing ${messages.length} messages for user: ${fullUserId}`)

        const operationData = {
          operation: 'add',
          messages: messages,
          userId: fullUserId,
          metadata: metadata
        }

        const result = await runMem0Operation('add', operationData)

        if (result.success) {
          console.log(`✅ Successfully processed memories for user: ${fullUserId}`)
        } else {
          console.error(`❌ Failed to process memories for user ${fullUserId}:`, result.error)
        }
      } catch (error) {
        console.error(`❌ Error processing memories for user ${fullUserId}:`, error.message)
      }
    })

  } catch (error) {
    console.error('Error in /add endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while processing your request'
    })
  }
})

// GET /search - Search memories synchronously
app.get('/search', authenticateToken, async (req, res) => {
  try {
    const { user_id, session_id, message, limit } = req.query

    // Validate required parameters
    if (!user_id || !session_id) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both user_id and session_id are required as query parameters'
      })
    }

    if (!message) {
      return res.status(400).json({
        error: 'Missing required parameter',
        message: 'message is required as a query parameter'
      })
    }

    // Build full user ID
    const fullUserId = buildUserId(user_id, session_id)

    // Parse limit or use default
    const searchLimit = limit ? parseInt(limit) : parseInt(process.env.DEFAULT_SEARCH_LIMIT)

    console.log(`🔍 Searching memories for user: ${fullUserId}, query: "${message}"`)

    try {
      const operationData = {
        operation: 'search',
        query: message,
        userId: fullUserId,
        limit: searchLimit
      }

      const result = await runMem0Operation('search', operationData)

      if (result.success) {
        console.log(`✅ Found ${result.result?.results?.length || 0} memories for user: ${fullUserId}`)

        // Return raw result from mem0
        res.json({
          status: 'success',
          user_id: fullUserId,
          query: message,
          limit: searchLimit,
          timestamp: new Date().toISOString(),
          ...result.result
        })
      } else {
        throw new Error(result.error || 'Search operation failed')
      }
    } catch (searchError) {
      console.error(`❌ Search failed for user ${fullUserId}:`, searchError.message)

      // Return error response
      res.status(500).json({
        error: 'Search failed',
        message: 'An error occurred while searching memories',
        user_id: fullUserId,
        query: message,
        timestamp: new Date().toISOString()
      })
      return
    }

  } catch (error) {
    console.error('Error in /search endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while searching memories'
    })
  }
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err)
  res.status(500).json({
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: 'The requested endpoint does not exist'
  })
})

// Start server
console.log('🔄 Starting server...')
app.listen(port, () => {
  console.log(`🚀 Mem0 REST API server running on port ${port}`)
  console.log(`📊 Configuration:`)
  console.log(`   - LLM Model: ${process.env.OPENAI_LLM_MODEL}`)
  console.log(`   - Embedding Model: ${process.env.OPENAI_EMBEDDING_MODEL}`)
  console.log(`   - Qdrant: ${process.env.QDRANT_HOST}:${process.env.QDRANT_PORT}`)
  console.log(`   - Collection: ${process.env.QDRANT_COLLECTION_NAME}`)
  console.log(`   - Graph Enabled: ${process.env.ENABLE_GRAPH}`)
  console.log(`   - Default Search Limit: ${process.env.DEFAULT_SEARCH_LIMIT}`)
  console.log(`🔐 Authentication: Bearer token required`)
  console.log(`📋 Available endpoints:`)
  console.log(`   - GET  /health`)
  console.log(`   - POST /add`)
  console.log(`   - GET  /search`)
  console.log(`✅ Server ready to accept connections!`)
})

app.on('error', (error) => {
  console.error('❌ Server error:', error)
})

console.log('📝 Server setup complete, waiting for listen callback...')
