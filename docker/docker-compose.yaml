services:
  qdrant:
    image: qdrant/qdrant:latest
    restart: always
    container_name: qdrant
    ports:
      - 6333:6333
      - 6334:6334
    expose:
      - 6333
      - 6334
      - 6335
    configs:
      - source: qdrant_config
        target: /qdrant/config/production.yaml
    volumes:
      - ../data/qdrant:/qdrant/storage
  neo4j:
    image: neo4j:latest
    volumes:
        - ../data/neo4j/logs:/logs
        - ../data/neo4j/config:/config
        - ../data/neo4j/data:/data
        - ../data/neo4j/plugins:/plugins
    environment:
        - NEO4J_AUTH=neo4j/Neo4JPassword23456
    ports:
      - "7474:7474"
      - "7687:7687"
    restart: always

configs:
  qdrant_config:
    content: |
      log_level: INFO