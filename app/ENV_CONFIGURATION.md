# Environment Configuration Guide

This document explains all the environment variables used in the mem0ai configuration.

## 📁 File Location
All environment variables are stored in `app/.env`

## 🔧 Configuration Categories

### 🤖 OpenAI Configuration
Configure OpenAI services for LLM and embeddings.

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `OPENAI_API_KEY` | Your OpenAI API key | Required | `sk-proj-...` |
| `OPENAI_LLM_MODEL` | OpenAI model for text generation | `gpt-4o-mini` | `gpt-4o`, `gpt-3.5-turbo` |
| `OPENAI_LLM_TEMPERATURE` | Temperature for text generation (0.0-2.0) | `0.7` | `0.1`, `1.0` |
| `OPENAI_EMBEDDING_MODEL` | OpenAI model for embeddings | `text-embedding-3-small` | `text-embedding-3-large` |

### 🗄️ Qdrant Vector Store Configuration
Configure Qdrant for vector storage and similarity search.

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `QDRANT_HOST` | Qdrant server hostname | `localhost` | `qdrant.example.com` |
| `QDRANT_PORT` | Qdrant server port | `6333` | `6333` |
| `QDRANT_COLLECTION_NAME` | Collection name for vectors | `memory_test` | `production_memories` |
| `QDRANT_EMBEDDING_DIMS` | Embedding dimensions | `1536` | `1536` (for text-embedding-3-small) |

### 📚 History Store Configuration
Configure SQLite database for memory history.

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `HISTORY_DB_PATH` | Full path to SQLite history database | `/home/<USER>/data/mem0/history.db` | `/app/data/history.db` |

### 🕸️ Neo4j Graph Store Configuration
Configure Neo4j for graph-based memory relationships.

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `NEO4J_URI` | Neo4j connection URI | `neo4j://localhost:7687` | `neo4j://neo4j.example.com:7687` |
| `NEO4J_USERNAME` | Neo4j username | `neo4j` | `admin` |
| `NEO4J_PASSWORD` | Neo4j password | `Neo4JPassword23456` | `your_secure_password` |

### 🚩 Feature Flags
Enable or disable specific features.

| Variable | Description | Default Value | Options |
|----------|-------------|---------------|---------|
| `ENABLE_GRAPH` | Enable Neo4j graph memory | `true` | `true`, `false` |

## 🔄 How to Modify Configuration

### 1. Edit Environment Variables
```bash
# Edit the .env file
nano app/.env
```

### 2. Restart Application
After changing environment variables, restart your application:
```bash
cd app
node index.js
```

## 🌍 Environment-Specific Configurations

### Development Environment
```env
OPENAI_LLM_MODEL="gpt-4o-mini"
OPENAI_LLM_TEMPERATURE="0.7"
QDRANT_COLLECTION_NAME="dev_memories"
ENABLE_GRAPH="true"
```

### Production Environment
```env
OPENAI_LLM_MODEL="gpt-4o"
OPENAI_LLM_TEMPERATURE="0.3"
QDRANT_COLLECTION_NAME="prod_memories"
ENABLE_GRAPH="true"
```

### Testing Environment
```env
OPENAI_LLM_MODEL="gpt-3.5-turbo"
OPENAI_LLM_TEMPERATURE="0.1"
QDRANT_COLLECTION_NAME="test_memories"
ENABLE_GRAPH="false"
```

## 🔒 Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use strong passwords** for Neo4j
3. **Rotate API keys** regularly
4. **Use environment-specific configurations**
5. **Limit API key permissions** when possible

## 🚨 Troubleshooting

### Common Issues

1. **Qdrant Connection Failed**
   - Check `QDRANT_HOST` and `QDRANT_PORT`
   - Ensure Qdrant is running: `curl http://localhost:6333/collections`

2. **Neo4j Authentication Failed**
   - Verify `NEO4J_PASSWORD` matches Docker Compose configuration
   - Check Neo4j is running: `curl http://localhost:7474`

3. **OpenAI API Errors**
   - Validate `OPENAI_API_KEY`
   - Check API quota and billing

4. **History Database Issues**
   - Ensure `HISTORY_DB_PATH` directory exists
   - Check file permissions

## 📊 Monitoring

### Check Configuration Loading
The application will show configuration values on startup (sensitive values are masked).

### Verify Services
```bash
# Check Qdrant
curl http://localhost:6333/collections

# Check Neo4j
curl http://localhost:7474

# Check history database
ls -la /path/to/history.db
```

## 🔄 Migration Guide

When moving between environments:

1. Copy `.env.example` to `.env`
2. Update all variables for your environment
3. Test connectivity to all services
4. Run application and verify functionality

## 📝 Notes

- All numeric values are automatically parsed (parseInt/parseFloat)
- Boolean values use string comparison (`=== 'true'`)
- Paths should be absolute for production deployments
- Environment variables override any hardcoded values
