const express = require('express')
const { spawn } = require('child_process')
const fs = require('fs').promises
const path = require('path')
require('dotenv').config()

const app = express()
const port = process.env.API_PORT || 3000

// Middleware
app.use(express.json())

// Helper function to run the existing index.js with custom messages
const runMemoryProcess = async (messages, userId) => {
  return new Promise((resolve, reject) => {
    // Create a temporary file with the messages
    const tempFile = path.join(process.cwd(), `temp_messages_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.json`)

    const processData = {
      messages,
      userId,
      timestamp: new Date().toISOString()
    }

    // Write messages to temp file
    fs.writeFile(tempFile, JSON.stringify(processData, null, 2))
      .then(() => {
        // Run the memory processing
        const child = spawn('node', ['-e', `
          const { Memory } = require('mem0ai/oss');
          const fs = require('fs');
          const dotenv = require('dotenv');

          dotenv.config();

          const config = {
            llm: {
              provider: 'openai',
              config: {
                apiKey: process.env.OPENAI_API_KEY,
                model: process.env.OPENAI_LLM_MODEL,
                temperature: parseFloat(process.env.OPENAI_LLM_TEMPERATURE)
              }
            },
            embedder: {
              provider: 'openai',
              config: {
                model: process.env.OPENAI_EMBEDDING_MODEL,
                apiKey: process.env.OPENAI_API_KEY
              }
            },
            vectorStore: {
              provider: 'qdrant',
              config: {
                collectionName: process.env.QDRANT_COLLECTION_NAME,
                host: process.env.QDRANT_HOST,
                port: parseInt(process.env.QDRANT_PORT),
                embeddingModelDims: parseInt(process.env.QDRANT_EMBEDDING_DIMS)
              }
            },
            historyStore: {
              provider: 'sqlite',
              config: {
                historyDbPath: process.env.HISTORY_DB_PATH
              }
            },
            enableGraph: process.env.ENABLE_GRAPH === 'true',
            graphStore: {
              provider: 'neo4j',
              config: {
                url: process.env.NEO4J_URI,
                username: process.env.NEO4J_USERNAME,
                password: process.env.NEO4J_PASSWORD
              }
            }
          };

          const memory = new Memory(config);
          const data = JSON.parse(fs.readFileSync('${tempFile}', 'utf8'));

          memory.add(data.messages, { userId: data.userId })
            .then(result => {
              console.log(JSON.stringify({ success: true, result }));
              process.exit(0);
            })
            .catch(error => {
              console.error(JSON.stringify({ success: false, error: error.message }));
              process.exit(1);
            });
        `], { stdio: 'pipe' })

        let output = ''
        let errorOutput = ''

        child.stdout.on('data', (data) => {
          output += data.toString()
        })

        child.stderr.on('data', (data) => {
          errorOutput += data.toString()
        })

        child.on('close', (code) => {
          // Clean up temp file
          fs.unlink(tempFile).catch(() => {})

          if (code === 0) {
            try {
              const result = JSON.parse(output.trim())
              resolve(result)
            } catch (e) {
              resolve({ success: true, output })
            }
          } else {
            reject(new Error(errorOutput || 'Process failed'))
          }
        })

        child.on('error', (error) => {
          // Clean up temp file
          fs.unlink(tempFile).catch(() => {})
          reject(error)
        })
      })
      .catch(reject)
  })
}

// Helper function to search memories
const searchMemories = async (query, userId, limit = 5) => {
  return new Promise((resolve, reject) => {
    const child = spawn('node', ['-e', `
      const { Memory } = require('mem0ai/oss');
      const dotenv = require('dotenv');

      dotenv.config();

      const config = {
        llm: {
          provider: 'openai',
          config: {
            apiKey: process.env.OPENAI_API_KEY,
            model: process.env.OPENAI_LLM_MODEL,
            temperature: parseFloat(process.env.OPENAI_LLM_TEMPERATURE)
          }
        },
        embedder: {
          provider: 'openai',
          config: {
            model: process.env.OPENAI_EMBEDDING_MODEL,
            apiKey: process.env.OPENAI_API_KEY
          }
        },
        vectorStore: {
          provider: 'qdrant',
          config: {
            collectionName: process.env.QDRANT_COLLECTION_NAME,
            host: process.env.QDRANT_HOST,
            port: parseInt(process.env.QDRANT_PORT),
            embeddingModelDims: parseInt(process.env.QDRANT_EMBEDDING_DIMS)
          }
        },
        historyStore: {
          provider: 'sqlite',
          config: {
            historyDbPath: process.env.HISTORY_DB_PATH
          }
        },
        enableGraph: process.env.ENABLE_GRAPH === 'true',
        graphStore: {
          provider: 'neo4j',
          config: {
            url: process.env.NEO4J_URI,
            username: process.env.NEO4J_USERNAME,
            password: process.env.NEO4J_PASSWORD
          }
        }
      };

      const memory = new Memory(config);

      memory.search('${query}', { userId: '${userId}', limit: ${limit} })
        .then(result => {
          console.log(JSON.stringify({ success: true, result }));
          process.exit(0);
        })
        .catch(error => {
          console.error(JSON.stringify({ success: false, error: error.message }));
          process.exit(1);
        });
    `], { stdio: 'pipe' })

    let output = ''
    let errorOutput = ''

    child.stdout.on('data', (data) => {
      output += data.toString()
    })

    child.stderr.on('data', (data) => {
      errorOutput += data.toString()
    })

    child.on('close', (code) => {
      if (code === 0) {
        try {
          const result = JSON.parse(output.trim())
          resolve(result)
        } catch (e) {
          resolve({ success: true, output })
        }
      } else {
        reject(new Error(errorOutput || 'Search failed'))
      }
    })

    child.on('error', (error) => {
      reject(error)
    })
  })
}

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      error: 'Access token required',
      message: 'Please provide a Bearer token in the Authorization header'
    })
  }

  if (token !== process.env.API_BEARER_TOKEN) {
    return res.status(403).json({
      error: 'Invalid token',
      message: 'The provided token is not valid'
    })
  }

  next()
}

// Helper function to build user ID
const buildUserId = (userId, sessionId) => {
  if (!userId || !sessionId) {
    throw new Error('Both user_id and session_id are required')
  }
  return `${userId}_${sessionId}`
}

// Async function to process memories in background
const processMemoriesAsync = async (messages, fullUserId, metadata = {}) => {
  try {
    console.log(`🔄 Processing ${messages.length} messages for user: ${fullUserId}`)

    const result = await memory.add(messages, {
      userId: fullUserId,
      metadata: metadata
    })

    console.log(`✅ Successfully processed memories for user: ${fullUserId}`)
    return result
  } catch (error) {
    console.error(`❌ Error processing memories for user ${fullUserId}:`, error)
    throw error
  }
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'mem0-api'
  })
})

// POST /add - Add memories asynchronously
app.post('/add', authenticateToken, async (req, res) => {
  try {
    const { user_id, session_id, messages, metadata = {} } = req.body

    // Validate required parameters
    if (!user_id || !session_id) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both user_id and session_id are required'
      })
    }

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: 'Invalid messages parameter',
        message: 'messages must be a non-empty array'
      })
    }

    // Validate message format
    for (const message of messages) {
      if (!message.role || !message.content) {
        return res.status(400).json({
          error: 'Invalid message format',
          message: 'Each message must have "role" and "content" properties'
        })
      }
    }

    // Build full user ID
    const fullUserId = buildUserId(user_id, session_id)

    // Respond immediately to client
    res.status(202).json({
      status: 'accepted',
      message: 'Memories are being processed in the background',
      user_id: fullUserId,
      message_count: messages.length,
      timestamp: new Date().toISOString()
    })

    // Process memories asynchronously in background
    setImmediate(() => {
      processMemoriesAsync(messages, fullUserId, metadata)
        .catch(error => {
          console.error(`Background processing failed for user ${fullUserId}:`, error)
        })
    })

  } catch (error) {
    console.error('Error in /add endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while processing your request'
    })
  }
})

// GET /search - Search memories synchronously
app.get('/search', authenticateToken, async (req, res) => {
  try {
    const { user_id, session_id, message, limit } = req.query

    // Validate required parameters
    if (!user_id || !session_id) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both user_id and session_id are required as query parameters'
      })
    }

    if (!message) {
      return res.status(400).json({
        error: 'Missing required parameter',
        message: 'message is required as a query parameter'
      })
    }

    // Build full user ID
    const fullUserId = buildUserId(user_id, session_id)

    // Parse limit or use default
    const searchLimit = limit ? parseInt(limit) : parseInt(process.env.DEFAULT_SEARCH_LIMIT)

    console.log(`🔍 Searching memories for user: ${fullUserId}, query: "${message}"`)

    // Search memories synchronously
    const searchResult = await memory.search(message, {
      userId: fullUserId,
      limit: searchLimit
    })

    console.log(`✅ Found ${searchResult.results?.length || 0} memories for user: ${fullUserId}`)

    // Return raw result from mem0
    res.json({
      status: 'success',
      user_id: fullUserId,
      query: message,
      limit: searchLimit,
      timestamp: new Date().toISOString(),
      ...searchResult
    })

  } catch (error) {
    console.error('Error in /search endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while searching memories'
    })
  }
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err)
  res.status(500).json({
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: 'The requested endpoint does not exist'
  })
})

// Start server
app.listen(port, () => {
  console.log(`🚀 Mem0 REST API server running on port ${port}`)
  console.log(`📊 Configuration:`)
  console.log(`   - LLM Model: ${process.env.OPENAI_LLM_MODEL}`)
  console.log(`   - Embedding Model: ${process.env.OPENAI_EMBEDDING_MODEL}`)
  console.log(`   - Qdrant: ${process.env.QDRANT_HOST}:${process.env.QDRANT_PORT}`)
  console.log(`   - Collection: ${process.env.QDRANT_COLLECTION_NAME}`)
  console.log(`   - Graph Enabled: ${process.env.ENABLE_GRAPH}`)
  console.log(`   - Default Search Limit: ${process.env.DEFAULT_SEARCH_LIMIT}`)
  console.log(`🔐 Authentication: Bearer token required`)
  console.log(`📋 Available endpoints:`)
  console.log(`   - GET  /health`)
  console.log(`   - POST /add`)
  console.log(`   - GET  /search`)
})