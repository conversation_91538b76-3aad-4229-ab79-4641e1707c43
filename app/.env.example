# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY="your_openai_api_key_here"
OPENAI_LLM_MODEL="gpt-4o-mini"
OPENAI_LLM_TEMPERATURE="0.7"
OPENAI_EMBEDDING_MODEL="text-embedding-3-small"

# Qdrant Vector Store Configuration
# Default values for local Docker setup
QDRANT_HOST="localhost"
QDRANT_PORT="6333"
QDRANT_COLLECTION_NAME="memory_test"
QDRANT_EMBEDDING_DIMS="1536"

# History Store Configuration
# Update path to match your project structure
HISTORY_DB_PATH="/path/to/your/project/data/mem0/history.db"

# Neo4j Graph Store Configuration
# Default values for local Docker setup
NEO4J_URI="neo4j://localhost:7687"
NEO4J_USERNAME="neo4j"
NEO4J_PASSWORD="Neo4JPassword23456"

# Feature Flags
ENABLE_GRAPH="true"

# REST API Configuration
API_PORT="3000"
API_BEARER_TOKEN="your_secure_bearer_token_here"
DEFAULT_SEARCH_LIMIT="5"

# =============================================================================
# ENVIRONMENT-SPECIFIC EXAMPLES
# =============================================================================

# Development Environment
# OPENAI_LLM_MODEL="gpt-4o-mini"
# OPENAI_LLM_TEMPERATURE="0.7"
# QDRANT_COLLECTION_NAME="dev_memories"
# ENABLE_GRAPH="true"

# Production Environment
# OPENAI_LLM_MODEL="gpt-4o"
# OPENAI_LLM_TEMPERATURE="0.3"
# QDRANT_COLLECTION_NAME="prod_memories"
# ENABLE_GRAPH="true"

# Testing Environment
# OPENAI_LLM_MODEL="gpt-3.5-turbo"
# OPENAI_LLM_TEMPERATURE="0.1"
# QDRANT_COLLECTION_NAME="test_memories"
# ENABLE_GRAPH="false"
