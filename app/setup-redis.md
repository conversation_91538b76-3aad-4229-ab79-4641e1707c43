# Redis Setup for BullMQ

## Quick Redis Setup

### Option 1: Docker (Recommended)
```bash
# Start Redis in Docker
docker run -d --name redis-mem0 -p 6379:6379 redis:alpine

# Check if <PERSON><PERSON> is running
docker ps | grep redis
```

### Option 2: Local Installation
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server

# macOS with Homebrew
brew install redis
brew services start redis

# Check if Redis is running
redis-cli ping
# Should return: PONG
```

### Option 3: Redis Cloud (Free Tier)
1. Go to https://redis.com/try-free/
2. Create a free account
3. Create a new database
4. Update .env with your Redis connection details:
   ```
   REDIS_HOST="your-redis-host.com"
   REDIS_PORT="your-redis-port"
   ```

## Testing Redis Connection
```bash
# Test Redis connection
redis-cli ping

# Or test from Node.js
node -e "
const Redis = require('ioredis');
const redis = new Redis();
redis.ping().then(result => {
  console.log('Redis connection:', result);
  process.exit(0);
}).catch(err => {
  console.error('Redis error:', err.message);
  process.exit(1);
});
"
```

## BullMQ Benefits

✅ **Security**: No temp files or subprocess execution
✅ **Scalability**: Can handle thousands of concurrent jobs
✅ **Reliability**: Jobs are persisted and can be retried
✅ **Monitoring**: Built-in job status tracking
✅ **Performance**: Much faster than subprocess approach
✅ **Production Ready**: Used by many production applications

## Queue Features

- **Job Retry**: Failed jobs are automatically retried (3 attempts)
- **Exponential Backoff**: Delays between retries increase exponentially
- **Job Cleanup**: Completed/failed jobs are automatically cleaned up
- **Concurrency**: Multiple jobs can be processed simultaneously
- **Persistence**: Jobs survive server restarts
