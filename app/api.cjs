const express = require('express')
require('dotenv').config()

console.log('🔧 Starting Mem0 REST API server...')
console.log('📊 Environment variables loaded')

const app = express()
const port = process.env.API_PORT || 3000

console.log(`🌐 Server will run on port: ${port}`)

// Middleware
app.use(express.json())
console.log('✅ Express middleware configured')

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ 
      error: 'Access token required',
      message: 'Please provide a Bearer token in the Authorization header'
    })
  }

  if (token !== process.env.API_BEARER_TOKEN) {
    return res.status(403).json({ 
      error: 'Invalid token',
      message: 'The provided token is not valid'
    })
  }

  next()
}

// Helper function to build user ID
const buildUserId = (userId, sessionId) => {
  if (!userId || !sessionId) {
    throw new Error('Both user_id and session_id are required')
  }
  return `${userId}_${sessionId}`
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'mem0-api'
  })
})

// POST /add - Add memories asynchronously
app.post('/add', authenticateToken, async (req, res) => {
  try {
    const { user_id, session_id, messages, metadata = {} } = req.body

    // Validate required parameters
    if (!user_id || !session_id) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both user_id and session_id are required'
      })
    }

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: 'Invalid messages parameter',
        message: 'messages must be a non-empty array'
      })
    }

    // Validate message format
    for (const message of messages) {
      if (!message.role || !message.content) {
        return res.status(400).json({
          error: 'Invalid message format',
          message: 'Each message must have "role" and "content" properties'
        })
      }
    }

    // Build full user ID
    const fullUserId = buildUserId(user_id, session_id)

    // Respond immediately to client
    res.status(202).json({
      status: 'accepted',
      message: 'Memories are being processed in the background',
      user_id: fullUserId,
      message_count: messages.length,
      timestamp: new Date().toISOString()
    })

    // Process memories asynchronously in background
    setImmediate(() => {
      console.log(`🔄 Processing ${messages.length} messages for user: ${fullUserId}`)
      // Here we would normally call the memory processing
      // For now, just log that it would be processed
      setTimeout(() => {
        console.log(`✅ Successfully processed memories for user: ${fullUserId}`)
      }, 1000)
    })

  } catch (error) {
    console.error('Error in /add endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while processing your request'
    })
  }
})

// GET /search - Search memories synchronously
app.get('/search', authenticateToken, async (req, res) => {
  try {
    const { user_id, session_id, message, limit } = req.query

    // Validate required parameters
    if (!user_id || !session_id) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both user_id and session_id are required as query parameters'
      })
    }

    if (!message) {
      return res.status(400).json({
        error: 'Missing required parameter',
        message: 'message is required as a query parameter'
      })
    }

    // Build full user ID
    const fullUserId = buildUserId(user_id, session_id)

    // Parse limit or use default
    const searchLimit = limit ? parseInt(limit) : parseInt(process.env.DEFAULT_SEARCH_LIMIT)

    console.log(`🔍 Searching memories for user: ${fullUserId}, query: "${message}"`)

    // Mock search result for now
    const mockResult = {
      results: [
        {
          id: "mem_123",
          memory: "John loves playing guitar and has been playing for 5 years",
          score: 0.95,
          metadata: {
            created_at: "2024-06-13T07:45:00Z",
            user_id: fullUserId
          }
        }
      ]
    }

    console.log(`✅ Found ${mockResult.results?.length || 0} memories for user: ${fullUserId}`)

    // Return result
    res.json({
      status: 'success',
      user_id: fullUserId,
      query: message,
      limit: searchLimit,
      timestamp: new Date().toISOString(),
      ...mockResult
    })

  } catch (error) {
    console.error('Error in /search endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while searching memories'
    })
  }
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err)
  res.status(500).json({
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: 'The requested endpoint does not exist'
  })
})

// Start server
app.listen(port, () => {
  console.log(`🚀 Mem0 REST API server running on port ${port}`)
  console.log(`📊 Configuration:`)
  console.log(`   - LLM Model: ${process.env.OPENAI_LLM_MODEL}`)
  console.log(`   - Embedding Model: ${process.env.OPENAI_EMBEDDING_MODEL}`)
  console.log(`   - Qdrant: ${process.env.QDRANT_HOST}:${process.env.QDRANT_PORT}`)
  console.log(`   - Collection: ${process.env.QDRANT_COLLECTION_NAME}`)
  console.log(`   - Graph Enabled: ${process.env.ENABLE_GRAPH}`)
  console.log(`   - Default Search Limit: ${process.env.DEFAULT_SEARCH_LIMIT}`)
  console.log(`🔐 Authentication: Bearer token required`)
  console.log(`📋 Available endpoints:`)
  console.log(`   - GET  /health`)
  console.log(`   - POST /add`)
  console.log(`   - GET  /search`)
})
