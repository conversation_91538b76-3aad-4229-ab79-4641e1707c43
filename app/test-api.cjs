#!/usr/bin/env node

const http = require('http')

console.log('🧪 Testing Mem0 REST API...')

// Test 1: Health Check
function testHealth() {
  return new Promise((resolve, reject) => {
    console.log('\n1️⃣ Testing Health Endpoint...')
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/health',
      method: 'GET'
    }
    
    const req = http.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode}`)
        console.log(`   Response: ${data}`)
        resolve({ status: res.statusCode, data })
      })
    })
    
    req.on('error', reject)
    req.setTimeout(5000, () => {
      req.destroy()
      reject(new Error('Health check timeout'))
    })
    req.end()
  })
}

// Test 2: Authentication (should fail)
function testAuth() {
  return new Promise((resolve, reject) => {
    console.log('\n2️⃣ Testing Authentication (should fail)...')
    
    const postData = JSON.stringify({ test: 'data' })
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/add',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    }
    
    const req = http.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode}`)
        console.log(`   Response: ${data}`)
        resolve({ status: res.statusCode, data })
      })
    })
    
    req.on('error', reject)
    req.setTimeout(10000, () => {
      req.destroy()
      reject(new Error('Auth test timeout'))
    })
    
    req.write(postData)
    req.end()
  })
}

// Test 3: Valid Add Request
function testAdd() {
  return new Promise((resolve, reject) => {
    console.log('\n3️⃣ Testing Add Endpoint with Authentication...')
    
    const postData = JSON.stringify({
      user_id: 'test_user',
      session_id: 'test_session',
      messages: [
        { role: 'user', content: 'Hello, I am testing the API' }
      ]
    })
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/add',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': 'Bearer mem0_secure_token_2024_dev'
      }
    }
    
    const req = http.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode}`)
        console.log(`   Response: ${data}`)
        resolve({ status: res.statusCode, data })
      })
    })
    
    req.on('error', reject)
    req.setTimeout(15000, () => {
      req.destroy()
      reject(new Error('Add test timeout'))
    })
    
    req.write(postData)
    req.end()
  })
}

// Run all tests
async function runTests() {
  try {
    await testHealth()
    await testAuth()
    await testAdd()
    console.log('\n✅ All tests completed!')
  } catch (error) {
    console.error('\n❌ Test failed:', error.message)
  }
}

runTests()
