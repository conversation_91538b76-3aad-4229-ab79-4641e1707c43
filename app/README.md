# Mem0 REST API Server

A RESTful API service for mem0ai with configurable queue backends and environment-based configuration.

## Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Environment
```bash
cp .env.example .env
# Edit .env with your configuration
```

### 3. Start Server
```bash
node api.cjs
```

## API Endpoints

### Health Check
```bash
GET /health
```

### Add Memories (Asynchronous)
```bash
POST /add
Authorization: Bearer <token>
Content-Type: application/json

{
  "user_id": "john",
  "session_id": "session123",
  "messages": [
    {"role": "user", "content": "I love playing guitar"},
    {"role": "assistant", "content": "That's great! How long have you been playing?"}
  ]
}
```

### Search Memories (Synchronous)
```bash
GET /search?user_id=john&session_id=session123&message=guitar&limit=5
Authorization: Bearer <token>
```

## Configuration

### Queue Types

#### Redis Queue (Production)
```env
QUEUE_TYPE="redis"
REDIS_HOST="localhost"
REDIS_PORT="6379"
```

#### In-Memory Queue (Development)
```env
QUEUE_TYPE="memory"
```

### Required Environment Variables
```env
# API Configuration
API_PORT="3000"
API_BEARER_TOKEN="your_secure_token"
DEFAULT_SEARCH_LIMIT="5"

# OpenAI Configuration
OPENAI_API_KEY="your_openai_key"
OPENAI_LLM_MODEL="gpt-4o-mini"
OPENAI_EMBEDDING_MODEL="text-embedding-3-small"

# Qdrant Configuration
QDRANT_HOST="localhost"
QDRANT_PORT="6333"
QDRANT_COLLECTION_NAME="memory_collection"
QDRANT_EMBEDDING_DIMS="1536"

# Storage Configuration
HISTORY_DB_PATH="./data/history.db"

# Neo4j Configuration (Optional)
NEO4J_URI="neo4j://localhost:7687"
NEO4J_USERNAME="neo4j"
NEO4J_PASSWORD="password"
ENABLE_GRAPH="true"
```

## Testing

Run comprehensive tests:
```bash
node comprehensive-test.cjs
```

This will:
- Add 20 different memories across multiple users
- Test 12 search queries
- Provide detailed success rate report

## Architecture

- **Authentication**: Bearer token validation
- **User IDs**: Composite format `user_id + "_" + session_id`
- **Async Processing**: Memory additions processed in background
- **Sync Search**: Immediate search results
- **Queue Options**: Redis (production) or in-memory (development)

## Dependencies

- **express**: Web framework
- **mem0ai**: Memory processing
- **bullmq**: Job queue (Redis mode)
- **ioredis**: Redis client
- **dotenv**: Environment configuration

## Development

### Redis Setup (Optional)
```bash
# Docker
docker run -d --name redis -p 6379:6379 redis:alpine

# Or use in-memory mode
echo 'QUEUE_TYPE="memory"' >> .env
```

### File Structure
```
app/
├── api.cjs                 # Main REST API server
├── comprehensive-test.cjs  # Test suite
├── .env                    # Environment configuration
├── .env.example           # Configuration template
├── package.json           # Dependencies
└── README.md              # This file
```

## Production Deployment

1. Set `QUEUE_TYPE="redis"`
2. Configure Redis connection
3. Set secure `API_BEARER_TOKEN`
4. Configure all required environment variables
5. Start with process manager (PM2, systemd, etc.)

## Features

- RESTful API with proper HTTP status codes
- Bearer token authentication
- Asynchronous memory processing
- Synchronous search with fast response
- Configurable queue backends
- Environment-based configuration
- Comprehensive error handling
- Production-ready architecture

## Security

- No subprocess execution vulnerabilities
- No temporary file creation
- Secure job queue processing
- Environment-based secrets management
