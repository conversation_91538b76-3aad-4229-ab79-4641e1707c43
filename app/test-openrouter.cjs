#!/usr/bin/env node

// Simple test to verify OpenRouter works with mem0ai
require('dotenv').config()

async function testOpenRouter() {
  console.log('Testing OpenRouter integration with mem0ai...')

  try {
    const { Memory } = require('mem0ai/oss')

    // Test 1: Try with just OPENROUTER_API_KEY environment variable
    console.log('\n1. Testing with OPENROUTER_API_KEY environment variable...')
    console.log('OPENROUTER_API_KEY set:', !!process.env.OPENROUTER_API_KEY)

    const config = {
      llm: {
        provider: 'openai',
        config: {
          model: 'anthropic/claude-3.5-sonnet',
          temperature: 0.7
        }
      },
      embedder: {
        provider: 'openai',
        config: {
          model: 'text-embedding-3-small',
          apiKey: process.env.OPENAI_API_KEY  // Use OpenAI key for embeddings
        }
      },
      vectorStore: {
        provider: 'qdrant',
        config: {
          collectionName: 'test_openrouter',
          host: process.env.QDRANT_HOST || 'localhost',
          port: parseInt(process.env.QDRANT_PORT) || 6333,
          embeddingModelDims: 1536
        }
      },
      historyStore: {
        provider: 'sqlite',
        config: {
          historyDbPath: './test_openrouter_history.db'
        }
      }
    }
    
    console.log('Config:', JSON.stringify(config, null, 2))
    console.log('OPENROUTER_API_KEY set:', !!process.env.OPENROUTER_API_KEY)
    console.log('OPENAI_API_KEY set:', !!process.env.OPENAI_API_KEY)
    
    const memory = new Memory(config)
    console.log('Memory instance created successfully')
    
    // Test 2: Add a simple memory
    console.log('\n2. Testing memory addition...')
    
    const messages = [
      { role: 'user', content: 'My name is Alice and I love testing OpenRouter integration' },
      { role: 'assistant', content: 'Nice to meet you Alice! How is the OpenRouter testing going?' },
      { role: 'user', content: 'It should work with Claude 3.5 Sonnet now' }
    ]
    
    const result = await memory.add(messages, {
      userId: 'test_user_openrouter',
      metadata: { test: 'openrouter_integration' },
      version: 2
    })
    
    console.log('Memory added successfully:', result)
    
    // Test 3: Search memories
    console.log('\n3. Testing memory search...')
    
    const searchResult = await memory.search('Alice OpenRouter', {
      userId: 'test_user_openrouter',
      limit: 3
    })
    
    console.log('Search results:', JSON.stringify(searchResult, null, 2))
    
    console.log('\n✅ OpenRouter integration test completed successfully!')
    
  } catch (error) {
    console.error('\n❌ OpenRouter test failed:', error.message)
    console.error('Full error:', error)
    
    // Additional debugging
    console.log('\nDebugging info:')
    console.log('- OPENROUTER_API_KEY length:', process.env.OPENROUTER_API_KEY?.length || 0)
    console.log('- OPENAI_API_KEY length:', process.env.OPENAI_API_KEY?.length || 0)
    console.log('- Node version:', process.version)
  }
}

// Run the test
testOpenRouter()
