import { Memory } from 'mem0ai/oss'
import dotenv from 'dotenv'

// Load environment variables from your .env file
dotenv.config()

console.log('Starting the mem0ai example...')

// Your configuration, updated to use secure environment variables
const config = {
  llm: {
    provider: 'openai',
    config: {
      apiKey: process.env.OPENAI_API_KEY,
      model: 'gpt-4o-mini',
      temperature: 0.7
    }
  },
  embedder: {
    provider: 'openai',
    config: {
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY
    }
  },
  vector_store: {
    provider: 'qdrant',
    config: {
      collection_name: 'memory_test',
      url: 'http://localhost:6333'
    //   host: 'localhost',
    //   port: 6333
    }
  },
  history_db_path: '../data/mem0/history.db', // Storing history in the current directory
  graph_store: {
    provider: 'neo4j',
    config: {
      url: process.env.NEO4J_URI,
      username: process.env.NEO4J_USERNAME,
      password: process.env.NEO4J_PASSWORD
    }
  }
}

// Example data simulating a user conversation

const _userMessages = [
  "My name is <PERSON> and I'm a software developer."
]

const userMessages = [
  "My name is Alex and I'm a software developer.",
  'I live in Amsterdam.',
  'My favorite hobby is hiking in the mountains.',
//   "I'm planning a trip to Switzerland next month.",
//   'I need to book a flight and a hotel for the first week of July.',
//   'My colleague, Sarah, recommended visiting the Jungfrau region.',
//   'I have a pet dog, a Golden Retriever named Buddy.',
//   'Buddy is 3 years old.',
//   "I'm looking for dog-friendly hotels in Switzerland.",
//   'I also need to remember to buy new hiking boots before the trip.'
]

async function runExample (config) {
  console.log('Initializing memory with the provided config...')
  const memory = new Memory(config)

  // A unique ID for the user whose memories we are storing
  const userId = 'user-alex-456'

  console.log(
    `\nAdding ${userMessages.length} messages to memory for user: ${userId}`
  )

  // Loop through the messages and add them to the memory
  for (const message of userMessages) {
    console.log(`  -> Adding: "${message}"`)
    await memory.add([{ role: 'user', content: message }], {
      userId: userId,
      metadata: {}
    })
  }

  console.log('\n✅ All messages have been added successfully.')

  // --- Part 2: Retrieving Information ---
  console.log("\nNow, let's retrieve the information we stored.")

  const query1 = 'Where am I planning to go and what do I need to buy?'
  console.log(`\n❓ Searching with query: "${query1}"`)
  const result1 = await memory.search(query1, { userId: userId, limit: 5 })
  console.log('🧠 Search Result:', result1)

  const query2 = 'Tell me about my pet.'
  console.log(`\n❓ Searching with query: "${query2}"`)
  const result2 = await memory.search(query2, { userId: userId, limit: 5 })
  console.log('🧠 Search Result:', result2)

  // You can also get all memories stored for the user
  console.log('\nFetching all stored memories for this user...')
  const allMemories = await memory.getAll({ userId: userId })
  console.log(`Found ${allMemories.length} total memories.`)
  // console.log("All memories:", allMemories); // Uncomment to see the full data
}

// Run the main function and catch any errors
runExample(config).catch(console.error)
