const express = require('express')
const { Queue, Worker } = require('bullmq')
const Redis = require('ioredis')
require('dotenv').config()

function _cl(message, ...args) {
  if(process.env.LOGGING === 'true')
  {
    console.log(`[${new Date().toISOString()}] ${message}`, ...args)
  }
}

_cl('Starting Mem0 REST API server...')
_cl('Environment variables loaded')

const app = express()
const port = process.env.API_PORT || 3000

_cl(`Server will run on port: ${port}`)

// Queue configuration based on environment
const queueType = process.env.QUEUE_TYPE || 'redis'
let redis = null
let memoryQueue = null
let inMemoryJobs = []

if (queueType === 'redis') {
  // Redis connection for BullMQ
  redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    maxRetriesPerRequest: null,
    retryDelayOnFailover: 100,
  })

  // BullMQ queue with Redis
  memoryQueue = new Queue('memory-processing', { connection: redis })
  _cl('Redis connection and BullMQ queue initialized')
} else {
  // Simple in-memory queue implementation
  _cl('In-memory queue initialized (development mode)')
}

// Middleware
app.use(express.json())
_cl('Express middleware configured')

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ 
      error: 'Access token required',
      message: 'Please provide a Bearer token in the Authorization header'
    })
  }

  if (token !== process.env.API_BEARER_TOKEN) {
    return res.status(403).json({ 
      error: 'Invalid token',
      message: 'The provided token is not valid'
    })
  }

  next()
}

// Helper function to build user ID
const buildUserId = (userId, sessionId) => {
  if (!userId || !sessionId) {
    throw new Error('Both user_id and session_id are required')
  }
  return `${userId}_${sessionId}`
}

// Initialize mem0 configuration
let memoryInstance = null

async function getMemoryInstance() {
  if (!memoryInstance) {
    const { Memory } = require('mem0ai/oss')

    // Determine LLM provider configuration
    const llmProvider = process.env.LLM_PROVIDER || 'openai'
    let llmConfig

    if (llmProvider === 'openrouter') {
      llmConfig = {
        provider: 'openrouter',
        config: {
          apiKey: process.env.OPENROUTER_API_KEY,
          model: process.env.OPENROUTER_LLM_MODEL,
          temperature: parseFloat(process.env.OPENROUTER_LLM_TEMPERATURE)
        }
      }
    } else {
      llmConfig = {
        provider: 'openai',
        config: {
          apiKey: process.env.OPENAI_API_KEY,
          model: process.env.OPENAI_LLM_MODEL,
          temperature: parseFloat(process.env.OPENAI_LLM_TEMPERATURE)
        }
      }
    }

    const config = {
      llm: llmConfig,
      embedder: {
        provider: 'openai',
        config: {
          model: process.env.OPENAI_EMBEDDING_MODEL,
          apiKey: process.env.OPENAI_API_KEY
        }
      },
      vectorStore: {
        provider: 'qdrant',
        config: {
          collectionName: process.env.QDRANT_COLLECTION_NAME,
          host: process.env.QDRANT_HOST,
          port: parseInt(process.env.QDRANT_PORT),
          embeddingModelDims: parseInt(process.env.QDRANT_EMBEDDING_DIMS)
        }
      },
      historyStore: {
        provider: 'sqlite',
        config: {
          historyDbPath: process.env.HISTORY_DB_PATH
        }
      },
      enableGraph: process.env.ENABLE_GRAPH === 'true',
      graphStore: {
        provider: 'neo4j',
        config: {
          url: process.env.NEO4J_URI,
          username: process.env.NEO4J_USERNAME,
          password: process.env.NEO4J_PASSWORD
        }
      }
    }

    memoryInstance = new Memory(config)
    _cl('Mem0 instance initialized')
  }

  return memoryInstance
}

// Worker initialization based on queue type
let memoryWorker = null

if (queueType === 'redis') {
  // BullMQ Worker for Redis queue
  memoryWorker = new Worker('memory-processing', async (job) => {
    const { messages, userId, metadata } = job.data

    try {
      _cl(`Processing memory job for user: ${userId}`)

      const memory = await getMemoryInstance()
      const result = await memory.add(messages, {
        userId,
        metadata: metadata || {},
        version: 2
      })

      _cl(`Successfully processed memory for user: ${userId}`)
      return { success: true, result }

    } catch (error) {
      console.error(`Failed to process memory for user ${userId}:`, error.message)
      throw error
    }
  }, { connection: redis, concurrency: 3, autorun: true })

  // Handle worker events
  memoryWorker.on('completed', (job) => {
    _cl(`Job ${job.id} completed for user: ${job.data.userId}`)
  })

  memoryWorker.on('failed', (job, err) => {
    console.error(`Job ${job.id} failed for user: ${job.data.userId}`, err.message)
  })

  _cl('BullMQ worker initialized')
} else {
  // Simple in-memory job processor
  async function processInMemoryJob(jobData) {
    const { messages, userId, metadata } = jobData

    try {
      _cl(`Processing in-memory job for user: ${userId}`)

      const memory = await getMemoryInstance()
      const result = await memory.add(messages, {
        userId,
        metadata: metadata || {},
        version: 2
      })

      _cl(`Successfully processed in-memory job for user: ${userId}`)
      return { success: true, result }

    } catch (error) {
      console.error(`Failed to process in-memory job for user ${userId}:`, error.message)
      throw error
    }
  }

  _cl('In-memory job processor initialized')
}

// Helper function for synchronous search operations
async function searchMemories(query, userId, limit = 5) {
  try {
    const memory = await getMemoryInstance()
    const result = await memory.search(query, { userId, limit })
    return { success: true, result }
  } catch (error) {
    console.error(`Search failed for user ${userId}:`, error.message)
    return { success: false, error: error.message }
  }
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'mem0-api'
  })
})

// POST /add - Add memories asynchronously
app.post('/add', authenticateToken, async (req, res) => {
  try {
    const { user_id, session_id, messages, metadata = {} } = req.body

    // Validate required parameters
    if (!user_id || !session_id) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both user_id and session_id are required'
      })
    }

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: 'Invalid messages parameter',
        message: 'messages must be a non-empty array'
      })
    }

    // Validate message format
    for (const message of messages) {
      if (!message.role || !message.content) {
        return res.status(400).json({
          error: 'Invalid message format',
          message: 'Each message must have "role" and "content" properties'
        })
      }
    }

    // Build full user ID
    const fullUserId = buildUserId(user_id, session_id)

    // Respond immediately to client
    res.status(202).json({
      status: 'accepted',
      message: 'Memories are being processed in the background',
      user_id: fullUserId,
      message_count: messages.length,
      timestamp: new Date().toISOString()
    })

    // Process job based on queue type
    if (queueType === 'redis') {
      // Add job to BullMQ queue for background processing
      try {
        const job = await memoryQueue.add('process-memories', {
          messages: messages,
          userId: fullUserId,
          metadata: metadata
        }, {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 10,
          removeOnFail: 5
        })

        _cl(`Queued memory processing job ${job.id} for user: ${fullUserId}`)
      } catch (queueError) {
        console.error(`Failed to queue memory processing for user ${fullUserId}:`, queueError.message)
      }
    } else {
      // Process in-memory job immediately in background
      setImmediate(async () => {
        try {
          const jobData = {
            messages: messages,
            userId: fullUserId,
            metadata: metadata
          }

          _cl(`Processing in-memory job for user: ${fullUserId}`)

          const memory = await getMemoryInstance()
          const result = await memory.add(messages, {
            userId: fullUserId,
            metadata: metadata || {},
            version: 2
          })

          _cl(`Successfully processed in-memory job for user: ${fullUserId}`)
        } catch (error) {
          console.error(`Failed to process in-memory job for user ${fullUserId}:`, error.message)
        }
      })
    }

  } catch (error) {
    console.error('Error in /add endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while processing your request'
    })
  }
})

// GET /search - Search memories synchronously
app.get('/search', authenticateToken, async (req, res) => {
  try {
    const { user_id, session_id, message, limit } = req.query

    // Validate required parameters
    if (!user_id || !session_id) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both user_id and session_id are required as query parameters'
      })
    }

    if (!message) {
      return res.status(400).json({
        error: 'Missing required parameter',
        message: 'message is required as a query parameter'
      })
    }

    // Build full user ID
    const fullUserId = buildUserId(user_id, session_id)

    // Parse limit or use default
    const searchLimit = limit ? parseInt(limit) : parseInt(process.env.DEFAULT_SEARCH_LIMIT)

    _cl(`Searching memories for user: ${fullUserId}, query: "${message}"`)

    try {
      const result = await searchMemories(message, fullUserId, searchLimit)

      if (result.success) {
        _cl(`Found ${result.result?.results?.length || 0} memories for user: ${fullUserId}`)

        // Return raw result from mem0
        res.json({
          status: 'success',
          user_id: fullUserId,
          query: message,
          limit: searchLimit,
          timestamp: new Date().toISOString(),
          ...result.result
        })
      } else {
        throw new Error(result.error || 'Search operation failed')
      }
    } catch (searchError) {
      console.error(`Search failed for user ${fullUserId}:`, searchError.message)

      // Return error response
      res.status(500).json({
        error: 'Search failed',
        message: 'An error occurred while searching memories',
        user_id: fullUserId,
        query: message,
        timestamp: new Date().toISOString()
      })
      return
    }

  } catch (error) {
    console.error('Error in /search endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while searching memories'
    })
  }
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err)
  res.status(500).json({
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: 'The requested endpoint does not exist'
  })
})

// Start server
_cl('Starting server...')
app.listen(port, () => {
  const llmProvider = process.env.LLM_PROVIDER || 'openai'
  const llmModel = llmProvider === 'openrouter' ? process.env.OPENROUTER_LLM_MODEL : process.env.OPENAI_LLM_MODEL

  _cl(`Mem0 REST API server running on port ${port}`)
  _cl(`Configuration:`)
  _cl(`   - Queue Type: ${queueType}`)
  _cl(`   - LLM Provider: ${llmProvider}`)
  _cl(`   - LLM Model: ${llmModel}`)
  _cl(`   - Embedding Model: ${process.env.OPENAI_EMBEDDING_MODEL}`)
  _cl(`   - Qdrant: ${process.env.QDRANT_HOST}:${process.env.QDRANT_PORT}`)
  _cl(`   - Collection: ${process.env.QDRANT_COLLECTION_NAME}`)
  _cl(`   - Graph Enabled: ${process.env.ENABLE_GRAPH}`)
  _cl(`   - Default Search Limit: ${process.env.DEFAULT_SEARCH_LIMIT}`)
  if (queueType === 'redis') {
    _cl(`   - Redis: ${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`)
  }
  _cl(`Authentication: Bearer token required`)
  _cl(`Available endpoints:`)
  _cl(`   - GET  /health`)
  _cl(`   - POST /add`)
  _cl(`   - GET  /search`)
  _cl(`Server ready to accept connections!`)
})

app.on('error', (error) => {
  console.error('Server error:', error)
})

_cl('Server setup complete, waiting for listen callback...')
