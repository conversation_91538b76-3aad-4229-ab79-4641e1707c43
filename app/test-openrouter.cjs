#!/usr/bin/env node

// Test OpenRouter integration with mem0ai using LangChain
require('dotenv').config()

async function testOpenRouter() {
  console.log('Testing OpenRouter integration with mem0ai via LangChain...')

  try {
    const { Memory } = require('mem0ai/oss')
    const { ChatOpenAI } = require('@langchain/openai')

    // Test 1: Create OpenRouter model via LangChain
    console.log('\n1. Creating OpenRouter model via LangChain...')
    console.log('OPENROUTER_API_KEY set:', !!process.env.OPENROUTER_API_KEY)

    const openrouterModel = new ChatOpenAI({
      model: 'anthropic/claude-3.5-sonnet',
      temperature: 0.7,
      apiKey: process.env.OPENROUTER_API_KEY,
      configuration: {
        baseURL: 'https://openrouter.ai/api/v1'
      }
    })

    console.log('OpenRouter model created successfully')

    const config = {
      llm: {
        provider: 'langchain',
        config: {
          model: openrouterModel
        }
      },
      embedder: {
        provider: 'openai',
        config: {
          model: 'text-embedding-3-small',
          apiKey: process.env.OPENAI_API_KEY  // Use OpenAI key for embeddings
        }
      },
      vectorStore: {
        provider: 'qdrant',
        config: {
          collectionName: 'test_openrouter',
          host: process.env.QDRANT_HOST || 'localhost',
          port: parseInt(process.env.QDRANT_PORT) || 6333,
          embeddingModelDims: 1536
        }
      },
      historyStore: {
        provider: 'sqlite',
        config: {
          historyDbPath: './test_openrouter_history.db'
        }
      }
    }
    
    console.log('Config structure:', {
      llm: { provider: config.llm.provider, model: 'ChatOpenAI instance' },
      embedder: config.embedder,
      vectorStore: config.vectorStore,
      historyStore: config.historyStore
    })

    const memory = new Memory(config)
    console.log('Memory instance created successfully')
    
    // Test 2: Add a simple memory
    console.log('\n2. Testing memory addition...')
    
    const messages = [
      { role: 'user', content: 'My name is Alice and I love testing OpenRouter integration' },
      { role: 'assistant', content: 'Nice to meet you Alice! How is the OpenRouter testing going?' },
      { role: 'user', content: 'It should work with Claude 3.5 Sonnet now' }
    ]
    
    const result = await memory.add(messages, {
      userId: 'test_user_openrouter',
      metadata: { test: 'openrouter_integration' },
      version: 2
    })
    
    console.log('Memory added successfully:', result)
    
    // Test 3: Search memories
    console.log('\n3. Testing memory search...')
    
    const searchResult = await memory.search('Alice OpenRouter', {
      userId: 'test_user_openrouter',
      limit: 3
    })
    
    console.log('Search results:', JSON.stringify(searchResult, null, 2))
    
    console.log('\n✅ OpenRouter integration test completed successfully!')
    
  } catch (error) {
    console.error('\n❌ OpenRouter test failed:', error.message)
    console.error('Full error:', error)
    
    // Additional debugging
    console.log('\nDebugging info:')
    console.log('- OPENROUTER_API_KEY length:', process.env.OPENROUTER_API_KEY?.length || 0)
    console.log('- OPENAI_API_KEY length:', process.env.OPENAI_API_KEY?.length || 0)
    console.log('- Node version:', process.version)
  }
}

// Run the test
testOpenRouter()
